import torch
import torch.optim as optim
from tqdm import tqdm

def train_hrm(model, dataloader, epochs, tokenizer, device="cuda"):
    """Boucle d'entraînement avec deep supervision et validation grammaticale"""
    from src.grammar_validator import AGIGrammarValidator
    
    optimizer = optim.AdamW(model.parameters(), lr=3e-4)
    loss_fn = torch.nn.CrossEntropyLoss(ignore_index=tokenizer.vocab['<pad>'])
    
    # Validateur grammatical
    validator = AGIGrammarValidator()
    
    model.to(device)
    model.train()
    
    for epoch in range(epochs):
        total_loss = 0
        valid_programs = 0
        total_programs = 0
        
        progress = tqdm(dataloader, desc=f"Epoch {epoch+1}/{epochs}")
        
        for grids, programs in progress:
            grids = grids.to(device)
            programs = programs.to(device)
            
            optimizer.zero_grad()
            
            # Forward pass avec deep supervision (implémentation paper HRM)
            segments, halt_probs = model(grids, programs, max_segments=3)
            
            # Deep supervision : perte sur chaque segment
            total_segment_loss = torch.tensor(0.0, device=grids.device, requires_grad=True)
            
            for seg_idx, seg_logits in enumerate(segments):
                # Teacher forcing: décaler les tokens
                logits = seg_logits[:, :-1, :]
                targets = programs[:, 1:]
                
                # Perte cross-entropy
                seg_loss = loss_fn(logits.reshape(-1, logits.size(-1)), 
                                 targets.reshape(-1))
                
                # Pondération décroissante pour les segments tardifs
                weight = 1.0 / (seg_idx + 1)
                total_segment_loss = total_segment_loss + weight * seg_loss
            
            # Perte ACT (Q-learning approximation)
            act_loss = 0
            if halt_probs:
                for halt_prob in halt_probs:
                    # Encourager l'arrêt après quelques segments
                    target_halt = torch.ones_like(halt_prob) * 0.7
                    act_loss += torch.nn.functional.mse_loss(halt_prob, target_halt)
            
            # Perte totale
            loss = total_segment_loss + 0.1 * act_loss
            
            # Validation grammaticale périodique
            if epoch % 10 == 0 and total_programs < 5:  # Quelques échantillons par epoch
                with torch.no_grad():
                    # Générer un programme pour validation
                    model.eval()
                    sample_output = model(grids[:1], max_segments=3)
                    model.train()
                    
                    # Décoder et valider
                    if sample_output[0]:  # Si des segments générés
                        pred_tokens = torch.argmax(sample_output[0][0], dim=-1)[0]
                        pred_program = tokenizer.detokenize(pred_tokens.cpu().tolist())
                        
                        is_valid, _ = validator.validate_program(pred_program)
                        if is_valid:
                            valid_programs += 1
                        
                        total_programs += 1
            
            # Backpropagation
            loss.backward()
            
            # Gradient clipping pour stabilité
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            total_loss += loss.item()
            
            progress.set_postfix({
                'loss': f"{loss.item():.4f}",
                'valid_rate': f"{valid_programs}/{total_programs}" if total_programs > 0 else "N/A"
            })
        
        avg_loss = total_loss / len(dataloader)
        valid_rate = valid_programs / max(total_programs, 1)
        
        print(f"Epoch {epoch+1} | Avg Loss: {avg_loss:.4f} | Valid Programs: {valid_rate:.2%}")
    
    # Sauvegarde du modèle
    torch.save(model.state_dict(), "hrm_arc_solver_improved.pth")
    print("[TRAINING] Modèle sauvegardé avec améliorations grammaticales")