"""
Comparaison entre l'ancien HRM et le HRM amélioré
Évalue les améliorations apportées par les corrections du paper
"""

import torch

from torch.utils.data import DataLoader
import time
import json
from datetime import datetime
from torch.utils.data import DataLoader
from config import Config
from src.arc_dataset import ArcDataset
from src.tokenizer import GrammarTokenizer
from models.hrm_model import GridToProgramHRM  # Ancien modèle
from tests.test_improved_hrm import ImprovedHRM      # Nouveau modèle
from src.grammar_validator import validate_generated_program

class HRMComparator:
    """Comparateur entre ancien et nouveau HRM"""
    
    def __init__(self, device="cuda"):
        self.device = device
        self.tokenizer = GrammarTokenizer()
        
        # Configuration
        cfg = Config()
        self.model_dim = cfg.MODEL_DIM
        self.n_heads = cfg.N_HEADS
        self.vocab_size = len(self.tokenizer.vocab)
        
        # Modèles
        self.old_model = GridToProgramHRM(
            model_dim=self.model_dim,
            n_heads=self.n_heads,
            grammar_vocab_size=self.vocab_size,
            N_cycles=cfg.N_CYCLES,
            T_steps=cfg.T_STEPS
        ).to(device)
        
        if ImprovedHRM is not None:
            self.new_model = ImprovedHRM(
                model_dim=self.model_dim,
                n_heads=self.n_heads,
                grammar_vocab_size=self.vocab_size,
                N_cycles=cfg.N_CYCLES,
                T_steps=cfg.T_STEPS
            ).to(device)
        else:
            self.new_model = None
        
        print(f"Ancien modèle: {self.count_parameters(self.old_model):.1f}M paramètres")
        print(f"Nouveau modèle: {self.count_parameters(self.new_model):.1f}M paramètres")
    
    def count_parameters(self, model):
        """Compte les paramètres d'un modèle"""
        return sum(p.numel() for p in model.parameters() if p.requires_grad) / 1e6
    
    def generate_with_old_model(self, grid, max_length=150):
        """Génère un programme avec l'ancien modèle"""
        self.old_model.eval()
        with torch.no_grad():
            if len(grid.shape) == 2:
                grid = grid.unsqueeze(0)
            
            # Forward pass simple
            segments, _ = self.old_model(grid, max_segments=3)
            
            if not segments:
                return [1, 2]  # <sos> <eos>
            
            # Prendre le dernier segment
            logits = segments[-1]
            
            # Génération greedy
            generated = [1]  # <sos>
            for i in range(min(max_length, logits.size(1) - 1)):
                next_token = int(torch.argmax(logits[0, i, :]).item())
                generated.append(next_token)
                if next_token == 2:  # <eos>
                    break
            
            return generated
    
    def evaluate_model_quality(self, model, dataloader, num_samples=20, model_name="Model"):
        """Évalue la qualité d'un modèle"""
        results = {
            'model_name': model_name,
            'programs_generated': 0,
            'valid_programs': 0,
            'avg_quality_score': 0.0,
            'avg_generation_time': 0.0,
            'grammar_errors': 0,
            'unk_tokens': 0,
            'malformed_coords': 0,
            'programs_with_init': 0,
            'programs_with_end': 0,
            'sample_programs': []
        }
        
        total_quality = 0.0
        total_time = 0.0
        
        model.eval()
        with torch.no_grad():
            sample_count = 0
            for grids, _ in dataloader:
                if sample_count >= num_samples:
                    break
                
                grids = grids.to(self.device)
                
                for i in range(min(2, grids.size(0))):
                    if sample_count >= num_samples:
                        break
                    
                    grid = grids[i]
                    
                    # Génération avec timing
                    start_time = time.time()
                    
                    if model_name == "Ancien HRM":
                        generated_tokens = self.generate_with_old_model(grid)
                    else:
                        generated_tokens = model.generate_program(grid, max_length=150)
                    
                    generation_time = time.time() - start_time
                    total_time += generation_time
                    
                    # Validation
                    program_str = self.tokenizer.detokenize(generated_tokens)
                    validation = validate_generated_program(program_str, self.tokenizer)
                    
                    # Calcul du score de qualité
                    quality_score = 0.0
                    if validation['is_valid']:
                        quality_score += 0.5
                        results['valid_programs'] += 1
                    
                    stats = validation['statistics']
                    if stats['has_init']:
                        quality_score += 0.2
                        results['programs_with_init'] += 1
                    if stats['has_end']:
                        quality_score += 0.1
                        results['programs_with_end'] += 1
                    if stats['unk_tokens'] == 0:
                        quality_score += 0.1
                    else:
                        results['unk_tokens'] += stats['unk_tokens']
                    if stats['malformed_coords'] == 0:
                        quality_score += 0.1
                    else:
                        results['malformed_coords'] += stats['malformed_coords']
                    
                    total_quality += quality_score
                    results['programs_generated'] += 1
                    
                    # Sauvegarder quelques exemples
                    if len(results['sample_programs']) < 5:
                        results['sample_programs'].append({
                            'program': program_str[:200] + "..." if len(program_str) > 200 else program_str,
                            'quality_score': quality_score,
                            'is_valid': validation['is_valid'],
                            'generation_time': generation_time,
                            'suggestions': validation['suggestions'][:3] if validation['suggestions'] else []
                        })
                    
                    sample_count += 1
        
        # Calcul des moyennes
        if results['programs_generated'] > 0:
            results['avg_quality_score'] = total_quality / results['programs_generated']
            results['avg_generation_time'] = total_time / results['programs_generated']
            results['valid_program_ratio'] = results['valid_programs'] / results['programs_generated']
            results['init_ratio'] = results['programs_with_init'] / results['programs_generated']
            results['end_ratio'] = results['programs_with_end'] / results['programs_generated']
        
        return results
    
    def compare_models(self, dataloader, num_samples=20):
        """Compare les deux modèles"""
        print("=== Comparaison des Modèles HRM ===\n")
        
        # Évaluation de l'ancien modèle
        print("Évaluation de l'ancien modèle...")
        old_results = self.evaluate_model_quality(
            self.old_model, dataloader, num_samples, "Ancien HRM"
        )
        
        # Évaluation du nouveau modèle
        print("Évaluation du nouveau modèle...")
        new_results = self.evaluate_model_quality(
            self.new_model, dataloader, num_samples, "HRM Amélioré"
        )
        
        # Comparaison
        comparison = self.generate_comparison_report(old_results, new_results)
        
        return comparison
    
    def generate_comparison_report(self, old_results, new_results):
        """Génère un rapport de comparaison détaillé"""
        
        def format_improvement(old_val, new_val, is_percentage=False):
            if old_val == 0:
                return "N/A"
            improvement = ((new_val - old_val) / old_val) * 100
            sign = "+" if improvement > 0 else ""
            if is_percentage:
                return f"{new_val:.1%} ({sign}{improvement:.1f}%)"
            else:
                return f"{new_val:.3f} ({sign}{improvement:.1f}%)"
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'comparison_summary': {},
            'detailed_results': {
                'old_model': old_results,
                'new_model': new_results
            }
        }
        
        # Résumé de comparaison
        summary = report['comparison_summary']
        
        summary['quality_score'] = {
            'old': old_results['avg_quality_score'],
            'new': new_results['avg_quality_score'],
            'improvement': format_improvement(old_results['avg_quality_score'], new_results['avg_quality_score'])
        }
        
        summary['valid_programs'] = {
            'old': old_results['valid_program_ratio'],
            'new': new_results['valid_program_ratio'],
            'improvement': format_improvement(old_results['valid_program_ratio'], new_results['valid_program_ratio'], True)
        }
        
        summary['generation_time'] = {
            'old': old_results['avg_generation_time'],
            'new': new_results['avg_generation_time'],
            'improvement': format_improvement(old_results['avg_generation_time'], new_results['avg_generation_time'])
        }
        
        summary['unk_tokens'] = {
            'old': old_results['unk_tokens'],
            'new': new_results['unk_tokens'],
            'reduction': old_results['unk_tokens'] - new_results['unk_tokens']
        }
        
        # Affichage du rapport
        print("\n" + "="*60)
        print("RAPPORT DE COMPARAISON HRM")
        print("="*60)
        
        print(f"\n📊 MÉTRIQUES PRINCIPALES:")
        print(f"Score de Qualité    : {summary['quality_score']['old']:.3f} → {summary['quality_score']['new']:.3f} ({summary['quality_score']['improvement']})")
        print(f"Programmes Valides  : {summary['valid_programs']['improvement']}")
        print(f"Temps de Génération : {summary['generation_time']['old']:.3f}s → {summary['generation_time']['new']:.3f}s")
        
        print(f"\n🔧 AMÉLIORATIONS TECHNIQUES:")
        print(f"Tokens <unk>        : {old_results['unk_tokens']} → {new_results['unk_tokens']} ({summary['unk_tokens']['reduction']} de moins)")
        print(f"Coords malformées   : {old_results['malformed_coords']} → {new_results['malformed_coords']}")
        print(f"Programmes avec INIT: {old_results['init_ratio']:.1%} → {new_results['init_ratio']:.1%}")
        print(f"Programmes avec END : {old_results['end_ratio']:.1%} → {new_results['end_ratio']:.1%}")
        
        print(f"\n📝 EXEMPLES DE PROGRAMMES:")
        print("\nAncien modèle:")
        for i, sample in enumerate(old_results['sample_programs'][:2]):
            print(f"  {i+1}. {sample['program'][:100]}...")
            print(f"     Qualité: {sample['quality_score']:.2f}, Valide: {sample['is_valid']}")
        
        print("\nNouveau modèle:")
        for i, sample in enumerate(new_results['sample_programs'][:2]):
            print(f"  {i+1}. {sample['program'][:100]}...")
            print(f"     Qualité: {sample['quality_score']:.2f}, Valide: {sample['is_valid']}")
        
        print("\n" + "="*60)
        
        return report
    
    def save_comparison_report(self, report, filename=None):
        """Sauvegarde le rapport de comparaison"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"hrm_comparison_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"Rapport sauvegardé: {filename}")

def main():
    """Lance la comparaison des modèles"""
    # Configuration
    cfg = Config()
    device = cfg.DEVICE
    
    # Dataset
    tokenizer = GrammarTokenizer()
    dataset = ArcDataset(cfg.DATA_DIR, tokenizer, cfg.MAX_PROG_LEN)
    dataloader = DataLoader(dataset, batch_size=4, shuffle=True)
    
    # Comparateur
    comparator = HRMComparator(device)
    
    # Comparaison
    report = comparator.compare_models(dataloader, num_samples=10)
    
    # Sauvegarde
    comparator.save_comparison_report(report)

if __name__ == "__main__":
    main()