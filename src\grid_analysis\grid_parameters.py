"""
Paramètres fondamentaux d'une grille ARC.
Niveau 0 : Informations de base calculables directement.
"""

import numpy as np
from collections import Counter
from typing import Dict, Any


class GridParameters:
    """
    Paramètres fondamentaux d'une grille ARC.
    Niveau de complexité 0 : calculs directs sur dimensions et couleurs.
    """
    
    def __init__(self, grid: np.ndarray):
        """Initialise avec une grille numpy 2D."""
        self.grid = grid
        self._compute_all_parameters()
    
    def _compute_all_parameters(self):
        """Calcule tous les paramètres de niveau 0."""
        self._compute_dimensional_parameters()
        self._compute_chromatic_parameters()
    
    def _compute_dimensional_parameters(self):
        """Paramètres dimensionnels de base."""
        self.height, self.width = self.grid.shape
        self.total_cells = self.height * self.width
    
    def _compute_chromatic_parameters(self):
        """Paramètres chromatiques de base."""
        # Distribution des couleurs
        self.color_distribution = Counter(self.grid.flatten())
        self.color_palette = set(self.color_distribution.keys())
        self.unique_colors_count = len(self.color_palette)
        
        # Couleur dominante (la plus fréquente)
        self.dominant_color = self.color_distribution.most_common(1)[0][0]
        self.dominant_color_count = self.color_distribution[self.dominant_color]
        
        # Pourcentage d'occupation pour chaque couleur
        self.color_percentages = {
            color: count / self.total_cells 
            for color, count in self.color_distribution.items()
        }
        
        # Détection si une couleur se détache vraiment (écart-type)
        self.has_standout_color = self._compute_color_standout()
        
        # Couleurs non-dominantes
        self.non_dominant_colors = self.color_palette - {self.dominant_color}
        
        # Densité colorée (pixels non-dominants)
        non_dominant_pixels = sum(
            count for color, count in self.color_distribution.items() 
            if color != self.dominant_color
        )
        self.colored_density = non_dominant_pixels / self.total_cells
    
    def _compute_color_standout(self) -> bool:
        """
        Détermine si la couleur dominante se détache vraiment des autres.
        Utilise l'écart-type pour mesurer la dispersion.
        """
        if self.unique_colors_count <= 1:
            return True
        
        # Calculer l'écart-type des fréquences
        frequencies = list(self.color_distribution.values())
        mean_freq = np.mean(frequencies)
        std_freq = np.std(frequencies)
        
        # La couleur dominante se détache si elle est à plus de 2 écarts-types
        dominant_freq = self.dominant_color_count
        return bool((dominant_freq - mean_freq) > (2 * std_freq))
    
    def _convert_to_json_serializable(self, obj) -> Any:
        """Convertit les types NumPy en types Python natifs pour JSON."""
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            # Garder les clés numériques comme int, pas string
            return {int(k) if isinstance(k, (int, np.integer)) else k: self._convert_to_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple, set)):
            return [self._convert_to_json_serializable(item) for item in obj]
        else:
            return obj
    
    def to_dict(self) -> Dict[str, Any]:
        """Retourne tous les paramètres sous forme de dictionnaire sérialisable JSON."""
        data = {
            # Paramètres dimensionnels
            'height': self.height,
            'width': self.width,
            'total_cells': self.total_cells,
            
            # Paramètres chromatiques
            'color_palette': list(self.color_palette),
            'color_distribution': dict(self.color_distribution),
            'color_percentages': self.color_percentages,
            'unique_colors_count': self.unique_colors_count,
            'dominant_color': self.dominant_color,
            'dominant_color_percentage': self.color_percentages[self.dominant_color],
            'has_standout_color': self.has_standout_color,
            'non_dominant_colors': list(self.non_dominant_colors),
            'colored_density': self.colored_density
        }
        
        # Convertir tous les types NumPy en types Python natifs
        return self._convert_to_json_serializable(data)
    
    def __str__(self) -> str:
        """Représentation textuelle des paramètres."""
        return f"""GridParameters({self.width}x{self.height}):
  - {self.unique_colors_count} couleurs: {sorted(self.color_palette)}
  - Couleur dominante: {self.dominant_color} ({self.color_percentages[self.dominant_color]:.1%})
  - Se détache: {'Oui' if self.has_standout_color else 'Non'}
  - Densité colorée: {self.colored_density:.1%}"""


def analyze_grid_basic(grid: np.ndarray) -> Dict[str, Any]:
    """
    Fonction utilitaire pour analyser une grille.
    Retourne les paramètres de niveau 0.
    """
    params = GridParameters(grid)
    return params.to_dict()


# Exemple d'utilisation et interface CLI
if __name__ == "__main__":
    import argparse
    import json
    import sys
    
    # Forcer l'encodage UTF-8 pour éviter les problèmes d'affichage
    try:
        if sys.stdout.encoding != 'utf-8':
            getattr(sys.stdout, 'reconfigure', lambda **_: None)(encoding='utf-8', errors='replace')
    except (AttributeError, TypeError):
        pass  # reconfigure n'est pas disponible sur cette version de Python
    
    def main():
        parser = argparse.ArgumentParser(description="Analyse des paramètres fondamentaux d'une grille ARC")
        parser.add_argument("--test", action="store_true", help="Exécuter un test avec une grille d'exemple")
        parser.add_argument("--taskid", help="ID de la tâche ARC à analyser (ex: 007bbfb7). Si omis, analyse toutes les tâches")
        parser.add_argument("--subset", choices=["training", "evaluation"], default="training",
                           help="Subset de données (default: training)")
        parser.add_argument("--example", choices=["train", "test"], default=None,
                           help="Type d'exemple (si omis, traite train ET test)")
        parser.add_argument("--number", type=int, default=None,
                           help="Numéro de l'exemple (si omis, traite tous les exemples)")
        parser.add_argument("--grid", choices=["input", "output"], default="input",
                           help="Grille à analyser (default: input)")
        args = parser.parse_args()
        
        if args.taskid:
            # Analyser une grille d'une tâche ARC réelle
            from pathlib import Path
            
            task_file = Path(f"../../arcdata/{args.subset}/{args.taskid}.json")
            if not task_file.exists():
                print(f"❌ Fichier de tâche non trouvé: {task_file}")
                return
            
            with open(task_file, 'r') as f:
                task_data = json.load(f)
            
            # Déterminer quels types d'exemples traiter
            example_types = ["train", "test"] if args.example is None else [args.example]
            
            print(f"🔍 Analyse des paramètres - Tâche {args.taskid}")
            if args.example is None:
                print(f"Subset: {args.subset} | Type: TRAIN+TEST | Grille: {args.grid}")
            else:
                print(f"Subset: {args.subset} | Type: {args.example} | Grille: {args.grid}")
            print("=" * 60)
            
            import os
            results_dir = f"results/{args.subset}"
            os.makedirs(results_dir, exist_ok=True)
            
            for example_type in example_types:
                examples = task_data.get(example_type, [])
                if not examples:
                    print(f"⚠️ Aucun exemple '{example_type}' trouvé")
                    continue
                
                # Déterminer quels numéros d'exemples traiter
                if args.number is None:
                    example_numbers = list(range(len(examples)))
                else:
                    if args.number >= len(examples):
                        print(f"❌ Exemple {example_type}[{args.number}] non trouvé (max: {len(examples)-1})")
                        continue
                    example_numbers = [args.number]
                
                print(f"\n=== Exemples {example_type.upper()} ===")
                
                for example_num in example_numbers:
                    example = examples[example_num]
                    
                    if args.grid not in example:
                        print(f"❌ Grille '{args.grid}' non trouvée dans {example_type}[{example_num}]")
                        continue
                    
                    # Analyser la grille spécifiée
                    grid_data = np.array(example[args.grid])
                    params = GridParameters(grid_data)
                    
                    if len(example_numbers) > 1 or len(example_types) > 1:
                        print(f"\n--- {example_type.capitalize()} {example_num} ---")
                    
                    # Affichage de la grille
                    print("Grille:")
                    for row in grid_data:
                        print("  " + " ".join(f"{cell:1d}" for cell in row))
                    
                    print(f"\n{params}")
                    
                    # Détails supplémentaires
                    print(f"\n📊 Détails:")
                    print(f"  • Forme: {grid_data.shape}")
                    print(f"  • Valeurs min/max: {grid_data.min()}/{grid_data.max()}")
                    print(f"  • Couleurs présentes: {sorted(params.color_palette)}")
                    
                    # Distribution détaillée
                    print(f"\n📈 Distribution détaillée:")
                    for color in sorted(params.color_palette):
                        count = params.color_distribution[color]
                        percentage = params.color_percentages[color]
                        bar = "█" * int(percentage * 20)
                        print(f"  Couleur {color}: {count:2d} cellules ({percentage:5.1%}) {bar}")
                    
                    # Sauvegarder en JSON pour inspection
                    output_file = f"{results_dir}/{args.taskid}_{example_type}{example_num}_{args.grid}_params.json"
                    with open(output_file, 'w') as f:
                        analysis_data = {
                            "metadata": {
                                "taskid": args.taskid,
                                "subset": args.subset,
                                "example_type": example_type,
                                "example_number": example_num,
                                "grid_type": args.grid
                            },
                            "grid_data": grid_data.tolist(),
                            "parameters": params.to_dict()
                        }
                        json.dump(analysis_data, f, indent=2)
                    print(f"\n💾 Analyse sauvegardée dans '{output_file}'")
        
        elif args.test:
            # Test avec une grille d'exemple
            test_grid = np.array([
                [0, 7, 7],
                [7, 7, 7], 
                [0, 7, 7]
            ])
            
            print("🔍 Test d'analyse des paramètres fondamentaux")
            print("=" * 50)
            
            params = GridParameters(test_grid)
            
            # Affichage de la grille
            print("Grille de test:")
            for row in test_grid:
                print("  " + " ".join(f"{cell:1d}" for cell in row))
            
            print(f"\n{params}")
            print(f"\nDictionnaire complet:")
            print(json.dumps(params.to_dict(), indent=2))
        
        else:
            # Analyser toutes les tâches du subset
            from pathlib import Path
            import os
            
            subset_dir = Path(f"../../arcdata/{args.subset}")
            if not subset_dir.exists():
                print(f"[-] Répertoire non trouvé: {subset_dir}")
                return
            
            task_files = [f for f in subset_dir.glob("*.json") if not f.name.endswith("_arc_analysis.json")]
            if not task_files:
                print(f"[-] Aucune tâche trouvée dans {subset_dir}")
                return
            
            # Configuration d'affichage
            example_types = ["train", "test"] if args.example is None else [args.example]
            
            if args.number is None and args.example is None:
                print(f"[*] Analyse des parametres de grilles - Toutes les taches du subset {args.subset} - TOUS LES EXEMPLES (TRAIN+TEST)")
                print(f"[*] {len(task_files)} taches trouvees")
                print(f"[*] Configuration: TRAIN+TEST ALL {args.grid}")
            elif args.number is None:
                print(f"[*] Analyse des parametres de grilles - Toutes les taches du subset {args.subset} - TOUS LES EXEMPLES")
                print(f"[*] {len(task_files)} taches trouvees")
                print(f"[*] Configuration: {args.example} ALL {args.grid}")
            else:
                example_desc = "TRAIN+TEST" if args.example is None else args.example
                print(f"[*] Analyse des parametres de grilles - Toutes les taches du subset {args.subset}")
                print(f"[*] {len(task_files)} taches trouvees")
                print(f"[*] Configuration: {example_desc} {args.number} {args.grid}")
            print("=" * 60)
            
            successful_analyses = 0
            failed_analyses = 0
            
            for task_file in sorted(task_files):
                task_id = task_file.stem
                try:
                    print(f"\n[>] Analyse de la tache {task_id}...")
                except (BrokenPipeError, OSError):
                    break
                
                try:
                    with open(task_file, 'r') as f:
                        task_data = json.load(f)
                    
                    results_dir = f"results/{args.subset}"
                    os.makedirs(results_dir, exist_ok=True)
                    
                    # Traiter chaque type d'exemple (train et/ou test)
                    for example_type in example_types:
                        examples = task_data.get(example_type, [])
                        if not examples:
                            continue
                        
                        # Déterminer quels exemples traiter
                        if args.number is None:
                            example_numbers = list(range(len(examples)))
                        else:
                            if args.number >= len(examples):
                                print(f"  [!] Exemple {example_type}[{args.number}] non trouvé")
                                failed_analyses += 1
                                continue
                            example_numbers = [args.number]
                        
                        # Traiter chaque exemple
                        for example_num in example_numbers:
                            if args.grid not in examples[example_num]:
                                print(f"  [!] Grille '{args.grid}' non trouvée dans {example_type}[{example_num}]")
                                failed_analyses += 1
                                continue
                            
                            # Extraire la grille
                            grid_data = np.array(examples[example_num][args.grid])
                            
                            # Analyser
                            params = GridParameters(grid_data)
                            
                            # Sauvegarder
                            output_file = f"{results_dir}/{task_id}_{example_type}{example_num}_{args.grid}_params.json"
                            
                            analysis_data = {
                                "metadata": {
                                    "task_id": task_id,
                                    "subset": args.subset,
                                    "example_type": example_type,
                                    "example_number": example_num,
                                    "grid_type": args.grid
                                },
                                "grid_data": grid_data.tolist(),
                                "parameters": params.to_dict()
                            }
                            
                            with open(output_file, 'w') as f:
                                json.dump(analysis_data, f, indent=2)
                            
                            # Afficher résumé
                            try:
                                if len(example_numbers) > 1 or len(example_types) > 1:
                                    print(f"  [+] {example_type}{example_num} - Taille: {params.height}x{params.width}, Couleurs: {params.unique_colors_count}, Densite: {params.colored_density:.2f}")
                                else:
                                    print(f"  [+] Termine - Taille: {params.height}x{params.width}, Couleurs: {params.unique_colors_count}, Densite: {params.colored_density:.2f}")
                            except (BrokenPipeError, OSError):
                                break
                            successful_analyses += 1
                    
                except Exception as e:
                    print(f"  [-] Erreur: {str(e)[:50]}...")
                    failed_analyses += 1
            
            print(f"\n[*] Resume final:")
            print(f"  [+] Analyses reussies: {successful_analyses}")
            print(f"  [-] Analyses echouees: {failed_analyses}")
            print(f"  [*] Resultats dans: results/{args.subset}/")
            
            if successful_analyses == 0:
                print("\n[!] Aucune analyse reussie. Utilisez --test pour un exemple ou --taskid pour une tache specifique.")
    
    main()