import json
import torch
# Constantes pour les chemins de données
from pathlib import Path
DATA_RAW = Path("arcdata/training")
DATA_PROGRAMS = Path("programs")
DATA_PROCESSED = Path("processed")

def load_arc_puzzle(json_path):
    """Charge un puzzle ARC depuis un fichier JSON"""
    with open(json_path) as f:
        data = json.load(f)
    return data["train"] + data["test"]

def parse_agi_program(txt_path):
    """Extrait le programme d'un fichier .agi.txt"""
    with open(txt_path) as f:
        return f.read().strip()

def grid_to_tensor(grid):
    """Convertit une grille 2D en tenseur PyTorch"""
    return torch.tensor(grid, dtype=torch.long)

def create_dataset():
    """Crée le dataset final à partir des fichiers bruts"""
    dataset = []
    
    for json_file in DATA_RAW.glob("*.json"):
        puzzle_id = json_file.stem
        agi_file = DATA_PROGRAMS / f"{puzzle_id}_TEST0_VALID.agi"
        
        if not agi_file.exists():
            continue
            
        puzzles = load_arc_puzzle(json_file)
        program = parse_agi_program(agi_file)
        
        # On utilise seulement le premier exemple d'entraînement
        input_grid = puzzles[0]["input"]
        dataset.append({
            "puzzle_id": puzzle_id,
            "input_grid": grid_to_tensor(input_grid),
            "program": program
        })
    
    return dataset

def save_processed_data(dataset):
    """Sauvegarde le dataset traité pour l'entraînement"""
    torch.save(dataset, DATA_PROCESSED / "dataset.pt")

if __name__ == "__main__":
    dataset = create_dataset()
    save_processed_data(dataset)
    print(f"Dataset créé avec {len(dataset)} exemples")