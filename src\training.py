import torch
import torch.optim as optim
import torch.nn as nn
from torch.utils.data import DataLoader

# Imports depuis le projet
from .arc_dataset import ArcDataset
from .tokenizer import GrammarTokenizer
from ..models.hrm_model import GridToProgramHRM
from ..config import Config

# Configuration
cfg = Config()
tokenizer = GrammarTokenizer()

# Initialisation du dataset et dataloader
dataset = ArcDataset(
    data_folder=cfg.DATA_DIR, 
    tokenizer=tokenizer,
    max_prog_len=cfg.MAX_PROG_LEN
)
dataloader = DataLoader(dataset, batch_size=cfg.BATCH_SIZE, shuffle=True)

# Initialisation du modèle
model = GridToProgramHRM(
    model_dim=cfg.MODEL_DIM,
    n_heads=cfg.N_HEADS,
    grammar_vocab_size=len(tokenizer.vocab),  # Utilisation de la taille dynamique
    N_cycles=cfg.N_CYCLES,
    T_steps=cfg.T_STEPS
)

# Optimiseur et perte
optimizer = optim.AdamW(model.parameters(), lr=1e-4)
criterion = nn.CrossEntropyLoss(ignore_index=0)  # Ignorer les tokens <pad>

# Configuration d'entraînement
epochs = cfg.EPOCHS

# Boucle d'entraînement
for epoch in range(epochs):
    total_loss = 0
    for i, (grids, programs) in enumerate(dataloader):
        optimizer.zero_grad()
        
        # Forward pass
        segments, _ = model(grids, programs, max_segments=5)
        
        # Calcul de la perte sur chaque segment
        loss = torch.tensor(0.0, device=grids.device, requires_grad=True)
        for seg in segments:
            # seg: [batch, seq_len, vocab_size]
            # On décale les prédictions et les cibles
            pred = seg[:, :-1, :].contiguous().view(-1, seg.size(-1))
            target = programs[:, 1:].contiguous().view(-1)
            loss = loss + criterion(pred, target)
        
        # Backpropagation
        loss.backward()
        optimizer.step()
        total_loss += loss.item()
        
        if i % 10 == 0:
            print(f"Epoch {epoch+1}, Batch {i}, Loss: {loss.item()}")
    
    print(f"Epoch {epoch+1}, Average Loss: {total_loss / len(dataloader)}")

# Sauvegarder le modèle
torch.save(model.state_dict(), "hrm_arc_solver.pth")