"""
Test du modèle HRM amélioré avec les corrections du paper
et intégration de la validation grammaticale
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Optional, Tuple

# Import des modules existants
from src.tokenizer import GrammarTokenizer
from src.grammar_validator import validate_generated_program

class RMSNorm(nn.Module):
    """RMSNorm comme dans le paper HRM"""
    def __init__(self, dim, eps=1e-8):
        super().__init__()
        self.scale = nn.Parameter(torch.ones(dim))
        self.eps = eps
    
    def forward(self, x):
        norm = torch.rsqrt(x.pow(2).mean(-1, keepdim=True) + self.eps)
        return x * norm * self.scale

class GLU(nn.Module):
    """Gated Linear Unit comme dans Llama"""
    def __init__(self, dim):
        super().__init__()
        self.gate = nn.Linear(dim, dim * 2, bias=False)
    
    def forward(self, x):
        gate, value = self.gate(x).chunk(2, dim=-1)
        return F.silu(gate) * value

class PaperCompliantTransformerBlock(nn.Module):
    """Transformer Block conforme au paper HRM"""
    def __init__(self, dim, n_heads):
        super().__init__()
        self.attention = nn.MultiheadAttention(dim, n_heads, batch_first=True, bias=False)
        self.glu = GLU(dim)
        self.rms_norm1 = RMSNorm(dim)
        self.rms_norm2 = RMSNorm(dim)
    
    def forward(self, x):
        # Post-Norm architecture comme dans le paper
        attn_out, _ = self.attention(x, x, x)
        x = self.rms_norm1(x + attn_out)
        
        glu_out = self.glu(x)
        x = self.rms_norm2(x + glu_out)
        return x

class ImprovedHRM(nn.Module):
    """
    HRM amélioré avec corrections du paper :
    - Gradient 1-step avec détachement
    - Deep supervision
    - Q-learning ACT
    - Architecture moderne (RMSNorm, GLU)
    """
    
    def __init__(self, model_dim, n_heads, grammar_vocab_size, max_grid_size=900, N_cycles=3, T_steps=5):
        super().__init__()
        self.model_dim = model_dim
        self.N = N_cycles
        self.T = T_steps
        self.vocab_size = grammar_vocab_size
        
        # Encodage de la grille
        self.cell_embedding = nn.Embedding(10, model_dim)
        self.grid_pos_embedding = nn.Embedding(max_grid_size, model_dim)
        
        # Modules HRM avec architecture moderne
        self.H_module = PaperCompliantTransformerBlock(model_dim, n_heads)
        self.L_module = PaperCompliantTransformerBlock(model_dim, n_heads)
        
        # Projections d'entrée
        self.l_input_projection = nn.Linear(3 * model_dim, model_dim, bias=False)
        self.h_input_projection = nn.Linear(2 * model_dim, model_dim, bias=False)
        
        # Q-learning ACT (halt/continue)
        self.q_head = nn.Sequential(
            nn.Linear(model_dim, 64, bias=False),
            nn.SiLU(),
            nn.Linear(64, 2, bias=False)  # [halt, continue]
        )
        
        # Décodeur de programme
        self.program_embedding = nn.Embedding(grammar_vocab_size, model_dim)
        self.decoder_transformer = PaperCompliantTransformerBlock(model_dim, n_heads)
        self.output_head = nn.Linear(model_dim, grammar_vocab_size, bias=False)
        
        # États initiaux (truncated normal comme dans le paper)
        self.z_H_init = nn.Parameter(torch.randn(1, 1, model_dim) * 0.02)
        self.z_L_init = nn.Parameter(torch.randn(1, 1, model_dim) * 0.02)
        
        # Validation grammaticale
        self.tokenizer = None  # Sera défini lors de l'entraînement
        
        print(f"HRM amélioré initialisé: {self.count_parameters():.1f}M paramètres")
    
    def count_parameters(self):
        """Compte le nombre de paramètres du modèle"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad) / 1e6
    
    def hierarchical_reasoning_with_detachment(self, grid_emb):
        """
        HRM avec gradient 1-step et détachement entre cycles
        Implémentation conforme au paper
        """
        batch_size, grid_len, _ = grid_emb.shape
        
        # États initiaux
        z_H = self.z_H_init.repeat(batch_size, 1, 1)
        z_L = self.z_L_init.repeat(batch_size, 1, 1)
        
        for cycle in range(self.N):
            # DÉTACHEMENT pour gradient 1-step (critique)
            if cycle > 0:
                z_H = z_H.detach()
            
            # Sauvegarder z_L initial pour ce cycle
            z_L_cycle_start = z_L.detach()
            
            # T étapes du module L (convergence locale)
            for step in range(self.T):
                # Entrée combinée pour L
                l_input = torch.cat([
                    z_L.repeat(1, grid_len, 1),
                    z_H.repeat(1, grid_len, 1),
                    grid_emb
                ], dim=-1)
                
                l_input_proj = self.l_input_projection(l_input)
                z_L_new = self.L_module(l_input_proj.mean(dim=1, keepdim=True))
                
                # Mise à jour avec momentum pour stabilité
                z_L = z_L + 0.1 * (z_L_new - z_L)
            
            # Mise à jour H (une fois par cycle)
            h_input = torch.cat([z_H, z_L], dim=-1)
            h_input_proj = self.h_input_projection(h_input)
            z_H = self.H_module(h_input_proj)
            
            # RESET partiel de L pour convergence hiérarchique
            z_L = 0.3 * z_L_cycle_start + 0.7 * z_L
        
        return z_H
    
    def compute_q_values_and_targets(self, states, rewards, next_states=None):
        """
        Q-learning pour ACT comme dans le paper
        
        Args:
            states: États HRM [batch, seq, dim]
            rewards: Récompenses binaires [batch]
            next_states: États suivants (optionnel)
        """
        # Q-values actuels
        q_values = torch.sigmoid(self.q_head(states.squeeze(1)))  # [batch, 2]
        q_halt, q_continue = q_values[:, 0], q_values[:, 1]
        
        # Targets Q-learning
        with torch.no_grad():
            if next_states is not None:
                next_q = torch.sigmoid(self.q_head(next_states.squeeze(1)))
                next_q_max = torch.max(next_q, dim=1)[0]
                q_targets_continue = next_q_max
            else:
                q_targets_continue = torch.zeros_like(q_continue)
            
            q_targets_halt = rewards.float()
        
        return q_values, torch.stack([q_targets_halt, q_targets_continue], dim=1)
    
    def forward_with_deep_supervision(self, grid, program_tokens=None, max_segments=5):
        """
        Forward pass avec deep supervision comme dans le paper
        Chaque segment est supervisé indépendamment
        """
        # Encodage de la grille
        batch_size, height, width = grid.shape
        flat_grid = grid.view(batch_size, -1)
        grid_pos = torch.arange(height*width, device=grid.device).unsqueeze(0)
        grid_emb = self.cell_embedding(flat_grid) + self.grid_pos_embedding(grid_pos)
        
        segments = []
        q_values_list = []
        losses = []
        
        for seg in range(max_segments):
            # HRM avec détachement (gradient 1-step)
            hrm_state = self.hierarchical_reasoning_with_detachment(grid_emb)
            
            # Q-values pour ACT
            q_values = torch.sigmoid(self.q_head(hrm_state.squeeze(1)))
            q_values_list.append(q_values)
            
            # Génération du programme
            if program_tokens is not None:
                # Teacher forcing avec décodeur Transformer
                prog_emb = self.program_embedding(program_tokens)
                
                # Ajouter l'état HRM comme contexte
                context = hrm_state.repeat(1, prog_emb.size(1), 1)
                decoder_input = prog_emb + context
                
                decoder_output = self.decoder_transformer(decoder_input)
                logits = self.output_head(decoder_output)
                
                segments.append(logits)
                
                # Loss pour ce segment (deep supervision)
                if seg < len(segments):
                    targets = program_tokens[:, 1:]  # Décalage pour teacher forcing
                    segment_loss = F.cross_entropy(
                        logits[:, :-1, :].reshape(-1, self.vocab_size),
                        targets.reshape(-1),
                        ignore_index=0  # Ignorer padding
                    )
                    losses.append(segment_loss)
            
            # Condition d'arrêt ACT
            halt_prob = q_values[:, 0]  # Probabilité de halt
            if not self.training and torch.mean(halt_prob) > 0.7:
                break
            
            # DÉTACHEMENT pour deep supervision (critique)
            hrm_state = hrm_state.detach()
        
        return segments, q_values_list, losses
    
    def generate_program(self, grid, max_length=200, temperature=0.8):
        """
        Génère un programme AGI pour une grille donnée
        Avec validation grammaticale intégrée
        """
        self.eval()
        with torch.no_grad():
            batch_size = 1
            device = grid.device
            
            # Encodage
            if len(grid.shape) == 2:
                grid = grid.unsqueeze(0)
            
            height, width = grid.shape[1], grid.shape[2]
            flat_grid = grid.view(batch_size, -1)
            grid_pos = torch.arange(height*width, device=device).unsqueeze(0)
            grid_emb = self.cell_embedding(flat_grid) + self.grid_pos_embedding(grid_pos)
            
            # HRM
            hrm_state = self.hierarchical_reasoning_with_detachment(grid_emb)
            
            # Génération séquentielle
            generated = [1]  # <sos>
            hidden = hrm_state
            
            for _ in range(max_length):
                # Token actuel
                current_token = torch.tensor([[generated[-1]]], device=device)
                token_emb = self.program_embedding(current_token)
                
                # Contexte HRM
                context = hidden.repeat(1, 1, 1)
                decoder_input = token_emb + context
                
                # Prédiction
                decoder_output = self.decoder_transformer(decoder_input)
                logits = self.output_head(decoder_output)
                
                # Sampling avec température
                probs = F.softmax(logits.squeeze(0).squeeze(0) / temperature, dim=-1)
                next_token = torch.multinomial(probs, 1).item()
                
                generated.append(int(next_token))
                
                # Arrêt si <eos>
                if next_token == 2:  # <eos>
                    break
            
            return generated
    
    def validate_and_improve_program(self, program_tokens, tokenizer):
        """
        Valide et améliore un programme généré
        Utilise la validation grammaticale
        """
        # Détokeniser
        program_str = tokenizer.detokenize(program_tokens)
        
        # Valider avec la grammaire
        validation = validate_generated_program(program_str, tokenizer)
        
        # Corrections automatiques si nécessaire
        if not validation['is_valid']:
            corrected_program = validation['cleaned_program']
            corrected_tokens = tokenizer.tokenize(corrected_program)
            
            return {
                'original': program_str,
                'corrected': corrected_program,
                'tokens': corrected_tokens,
                'validation': validation
            }
        
        return {
            'original': program_str,
            'corrected': program_str,
            'tokens': program_tokens,
            'validation': validation
        }

def test_improved_hrm():
    """Test du modèle HRM amélioré"""
    print("=== Test du HRM Amélioré ===")
    
    # Configuration
    model_dim = 256
    n_heads = 8
    N_cycles = 3
    T_steps = 5
    
    # Tokenizer
    tokenizer = GrammarTokenizer()
    vocab_size = len(tokenizer.vocab)
    
    # Modèle
    model = ImprovedHRM(
        model_dim=model_dim,
        n_heads=n_heads,
        grammar_vocab_size=vocab_size,
        N_cycles=N_cycles,
        T_steps=T_steps
    )
    
    # Test avec grille dummy
    dummy_grid = torch.randint(0, 10, (2, 10, 10))
    dummy_program = torch.randint(1, vocab_size-1, (2, 50))
    
    print(f"Grille d'entrée: {dummy_grid.shape}")
    print(f"Programme cible: {dummy_program.shape}")
    
    # Test forward avec deep supervision
    segments, q_values, losses = model.forward_with_deep_supervision(
        dummy_grid, dummy_program, max_segments=3
    )
    
    print(f"Segments générés: {len(segments)}")
    print(f"Q-values: {len(q_values)}")
    print(f"Losses: {len(losses)}")
    
    if segments:
        print(f"Forme du premier segment: {segments[0].shape}")
    
    # Test génération
    print("\n=== Test Génération ===")
    generated_tokens = model.generate_program(dummy_grid[0], max_length=100)
    print(f"Tokens générés: {len(generated_tokens)}")
    
    # Validation grammaticale
    validation_result = model.validate_and_improve_program(generated_tokens, tokenizer)
    print(f"Programme original: {validation_result['original'][:100]}...")
    print(f"Validation: {validation_result['validation']['is_valid']}")
    
    if validation_result['validation']['suggestions']:
        print(f"Suggestions: {validation_result['validation']['suggestions']}")
    
    print("\n=== Test Réussi ===")

if __name__ == "__main__":
    test_improved_hrm()